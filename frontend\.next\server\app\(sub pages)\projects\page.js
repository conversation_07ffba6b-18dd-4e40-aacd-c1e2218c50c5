/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(sub pages)/projects/page";
exports.ids = ["app/(sub pages)/projects/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(sub%20pages)%2Fprojects%2Fpage&page=%2F(sub%20pages)%2Fprojects%2Fpage&appPaths=%2F(sub%20pages)%2Fprojects%2Fpage&pagePath=private-next-app-dir%2F(sub%20pages)%2Fprojects%2Fpage.js&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(sub%20pages)%2Fprojects%2Fpage&page=%2F(sub%20pages)%2Fprojects%2Fpage&appPaths=%2F(sub%20pages)%2Fprojects%2Fpage&pagePath=private-next-app-dir%2F(sub%20pages)%2Fprojects%2Fpage.js&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(sub pages)',\n        {\n        children: [\n        'projects',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(sub pages)/projects/page.js */ \"(rsc)/./src/app/(sub pages)/projects/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(sub pages)/layout.js */ \"(rsc)/./src/app/(sub pages)/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(sub pages)/projects/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(sub pages)/projects/page\",\n        pathname: \"/projects\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(sub%20pages)%2Fprojects%2Fpage&page=%2F(sub%20pages)%2Fprojects%2Fpage&appPaths=%2F(sub%20pages)%2Fprojects%2Fpage&pagePath=private-next-app-dir%2F(sub%20pages)%2Fprojects%2Fpage.js&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cpublic%5C%5Cbackground%5C%5Cprojects-background.png%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cmodels%5C%5CStaff.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cprojects%5C%5Cindex.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CRenderModel.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cpublic%5C%5Cbackground%5C%5Cprojects-background.png%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cmodels%5C%5CStaff.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cprojects%5C%5Cindex.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CRenderModel.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./public/background/projects-background.png */ \"(ssr)/./public/background/projects-background.png\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/models/Staff.jsx */ \"(ssr)/./src/components/models/Staff.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/projects/index.jsx */ \"(ssr)/./src/components/projects/index.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/RenderModel.jsx */ \"(ssr)/./src/components/RenderModel.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cpublic%5C%5Cbackground%5C%5Cprojects-background.png%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cmodels%5C%5CStaff.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cprojects%5C%5Cindex.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CRenderModel.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CFireFliesBackground.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CSound.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CFireFliesBackground.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CSound.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FireFliesBackground.jsx */ \"(ssr)/./src/components/FireFliesBackground.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Sound.jsx */ \"(ssr)/./src/components/Sound.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CFireFliesBackground.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CSound.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CHomeBtn.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CHomeBtn.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/HomeBtn.jsx */ \"(ssr)/./src/components/HomeBtn.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hQJTVDJTVDRGVza3RvcCU1QyU1Q0NsYXNzcm9vbSU1QyU1Q1Byb2plY3RzJTVDJTVDUG9ydGZvbGlvJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDSG9tZUJ0bi5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBZ0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtY3JlYXRpdmUtcG9ydGZvbGlvLz81YWIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEhQXFxcXERlc2t0b3BcXFxcQ2xhc3Nyb29tXFxcXFByb2plY3RzXFxcXFBvcnRmb2xpb1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxIb21lQnRuLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CClassroom%5C%5CProjects%5C%5CPortfolio%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CHomeBtn.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/FireFliesBackground.jsx":
/*!************************************************!*\
  !*** ./src/components/FireFliesBackground.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst createFirefly = ()=>({\n        id: Math.random(),\n        top: `${Math.random() * 100}%`,\n        left: `${Math.random() * 100}%`,\n        animationDuration: `${Math.random() * 5 + 5}s`\n    });\nconst FireFliesBackground = ()=>{\n    const [fireflies, setFireflies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const addFireflyPeriodically = ()=>{\n            const newFirefly = createFirefly();\n            setFireflies((currentFireflies)=>[\n                    ...currentFireflies.slice(-14),\n                    newFirefly\n                ]);\n        };\n        const interval = setInterval(addFireflyPeriodically, 1000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 w-full h-full -z-10 overflow-hidden\",\n        children: fireflies.map((firefly)=>{\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute roudned-full w-[10px] h-[10px] bg-firefly-radial\",\n                style: {\n                    top: firefly.top,\n                    left: firefly.left,\n                    animation: `move ${firefly.animationDuration} infinite alternate`\n                }\n            }, firefly.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\FireFliesBackground.jsx\",\n                lineNumber: 32,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\FireFliesBackground.jsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FireFliesBackground);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FireFliesBackground.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HomeBtn.jsx":
/*!************************************!*\
  !*** ./src/components/HomeBtn.jsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst NavLink = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst HomeBtn = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n        initial: {\n            scale: 0\n        },\n        animate: {\n            scale: 1\n        },\n        transition: {\n            delay: 1\n        },\n        href: \"/\",\n        target: \"_self\",\n        className: \"text-foreground  rounded-full flex items-center justify-center custom-bg fixed top-4 left-4 w-fit self-start z-50 \",\n        \"aria-label\": \"home\",\n        name: \"home\",\n        prefetch: false,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative  w-14 h-14 p-4  hover:text-accent\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-full h-auto\",\n                        strokeWidth: 1.5\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"peer bg-transparent absolute top-0 left-0 w-full h-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute hidden peer-hover:block px-2 py-1 left-full mx-2 top-1/2 -translate-y-1/2 bg-background text-foreground text-sm rounded-md shadow-lg whitespace-nowrap\",\n                        children: \"Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Go to Home Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\HomeBtn.jsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeBtn);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HomeBtn.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RenderModel.jsx":
/*!****************************************!*\
  !*** ./src/components/RenderModel.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst RenderModel = ({ children, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"w-screen h-screen -z-10 relative\", className),\n        shadows: false,\n        dpr: [\n            1,\n            2\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                fallback: null,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\RenderModel.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Environment, {\n                preset: \"dawn\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\RenderModel.jsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\RenderModel.jsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RenderModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9SZW5kZXJNb2RlbC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQ2dEO0FBQ0o7QUFDcEI7QUFDZ0I7QUFFeEMsTUFBTUssY0FBYyxDQUFDLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFO0lBQzFDLHFCQUNFLDhEQUFDTixzREFBTUE7UUFDTE0sV0FBV0wsZ0RBQUlBLENBQUMsb0NBQW9DSztRQUNwREMsU0FBUztRQUNUQyxLQUFLO1lBQUM7WUFBRztTQUFFOzswQkFHWCw4REFBQ0wsMkNBQVFBO2dCQUFDTSxVQUFVOzBCQUFPSjs7Ozs7OzBCQUMzQiw4REFBQ04sMERBQVdBO2dCQUFDVyxRQUFPOzs7Ozs7Ozs7Ozs7QUFHMUI7QUFFQSxpRUFBZU4sV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1jcmVhdGl2ZS1wb3J0Zm9saW8vLi9zcmMvY29tcG9uZW50cy9SZW5kZXJNb2RlbC5qc3g/MWQ5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IEVudmlyb25tZW50IH0gZnJvbSBcIkByZWFjdC10aHJlZS9kcmVpXCI7XG5pbXBvcnQgeyBDYW52YXMgfSBmcm9tIFwiQHJlYWN0LXRocmVlL2ZpYmVyXCI7XG5pbXBvcnQgY2xzeCBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IFJlYWN0LCB7IFN1c3BlbnNlIH0gZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IFJlbmRlck1vZGVsID0gKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPENhbnZhc1xuICAgICAgY2xhc3NOYW1lPXtjbHN4KFwidy1zY3JlZW4gaC1zY3JlZW4gLXotMTAgcmVsYXRpdmVcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHNoYWRvd3M9e2ZhbHNlfVxuICAgICAgZHByPXtbMSwgMl19XG4gICAgICAvLyBkcHIgaXMgdGhlIGRldmljZSBwaXhlbCByYXRpby4gSGVyZSB3ZSBhcmUgc2V0dGluZyBpdCB0byAxIGFuZCAyIGZvciByZXRpbmEgZGlzcGxheXMgdG8gcHJldmVudCBibHVycmluZXNzIGluIHRoZSBtb2RlbCByZW5kZXJpbmcgb24gaGlnaCByZXNvbHV0aW9uIHNjcmVlbnMuXG4gICAgPlxuICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXtudWxsfT57Y2hpbGRyZW59PC9TdXNwZW5zZT5cbiAgICAgIDxFbnZpcm9ubWVudCBwcmVzZXQ9XCJkYXduXCIgLz5cbiAgICA8L0NhbnZhcz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFJlbmRlck1vZGVsO1xuIl0sIm5hbWVzIjpbIkVudmlyb25tZW50IiwiQ2FudmFzIiwiY2xzeCIsIlJlYWN0IiwiU3VzcGVuc2UiLCJSZW5kZXJNb2RlbCIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwic2hhZG93cyIsImRwciIsImZhbGxiYWNrIiwicHJlc2V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RenderModel.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sound.jsx":
/*!**********************************!*\
  !*** ./src/components/Sound.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Modal = ({ onClose, toggle })=>{\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-background/60 backdrop-blur-sm flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background/20 border border-accent/30 border-solid backdrop-blur-[6px] py-8 px-6 xs:px-10 sm:px-16 rounded shadow-glass-inset text-center space-y-8 \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-light\",\n                    children: \"Do you like to play the background music?\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggle,\n                            className: \"px-4 py-2 border border-accent/30 border-solid hover:shadow-glass-sm rounded mr-2\",\n                            children: \"Yes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"px-4 py-2 border border-accent/30 border-solid hover:shadow-glass-sm rounded\",\n                            children: \"No\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined), document.getElementById(\"my-modal\"));\n};\nconst Sound = ()=>{\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleFirstUserInteraction = ()=>{\n        const musicConsent = localStorage.getItem(\"musicConsent\");\n        if (musicConsent === \"true\" && !isPlaying) {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n        [\n            \"click\",\n            \"keydown\",\n            \"touchstart\"\n        ].forEach((event)=>document.removeEventListener(event, handleFirstUserInteraction));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const consent = localStorage.getItem(\"musicConsent\");\n        const consentTime = localStorage.getItem(\"consentTime\");\n        if (consent && consentTime && new Date(consentTime).getTime() + 3 * 24 * 60 * 60 * 1000 > new Date()) {\n            setIsPlaying(consent === \"true\");\n            if (consent === \"true\") {\n                [\n                    \"click\",\n                    \"keydown\",\n                    \"touchstart\"\n                ].forEach((event)=>document.addEventListener(event, handleFirstUserInteraction));\n            }\n        } else {\n            setShowModal(true);\n        }\n    }, []);\n    const toggle = ()=>{\n        const newState = !isPlaying;\n        setIsPlaying(!isPlaying);\n        newState ? audioRef.current.play() : audioRef.current.pause();\n        localStorage.setItem(\"musicConsent\", String(newState));\n        localStorage.setItem(\"consentTime\", new Date().toISOString());\n        setShowModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-2.5 xs:right-4 z-50 group\",\n        children: [\n            showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                onClose: ()=>setShowModal(false),\n                toggle: toggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                loop: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: \"/audio/birds39-forest-20772.mp3\",\n                        type: \"audio/mpeg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    \"your browser does not support the audio element.\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                onClick: toggle,\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    delay: 1\n                },\n                className: \"w-10 h-10 xs:w-14 xs:h-14 text-foreground rounded-full flex items-center justify-center cursor-pointer z-50 p-2.5 xs:p-4 custom-bg\",\n                \"aria-label\": \"Sound control button\",\n                name: \"Sound control button\",\n                children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-full h-full text-foreground group-hover:text-accent\",\n                    strokeWidth: 1.5\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-full h-full text-foreground group-hover:text-accent\",\n                    strokeWidth: 1.5\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\Sound.jsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sound);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sound.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/models/Staff.jsx":
/*!*****************************************!*\
  !*** ./src/components/models/Staff.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/useGLTF.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\");\n/*\nAuto-generated by: https://github.com/pmndrs/gltfjsx\n*/ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Staff = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function Staff(props) {\n    // Use React.memo for performance optimization\n    const { nodes, materials } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)(\"/models/staff-transformed.glb\");\n    const modelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)(()=>{\n        modelRef.current.rotation.y += 0.007;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"group\", {\n        ...props,\n        dispose: null,\n        scale: [\n            3,\n            3,\n            3\n        ],\n        position: [\n            0,\n            -2,\n            0\n        ],\n        ref: modelRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                castShadow: true,\n                receiveShadow: true,\n                geometry: nodes.Wizard_Staff3_Wizard_Staff3_0.geometry,\n                material: materials.Wizard_Staff3,\n                position: [\n                    -0.041,\n                    0.983,\n                    0.768\n                ],\n                rotation: [\n                    0,\n                    Math.PI / 2,\n                    0\n                ],\n                scale: 0.04\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                castShadow: true,\n                receiveShadow: true,\n                geometry: nodes.Wizard_Staff3_Wizard_Staff3_0_1.geometry,\n                material: materials.Wizard_Staff3,\n                position: [\n                    -0.041,\n                    0.983,\n                    0.768\n                ],\n                rotation: [\n                    0,\n                    Math.PI / 2,\n                    0\n                ],\n                scale: 0.04\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                castShadow: true,\n                receiveShadow: true,\n                geometry: nodes.Wizard_Staff3_Wizard_Staff3_0_2.geometry,\n                material: materials.Wizard_Staff3,\n                position: [\n                    -0.041,\n                    0.983,\n                    0.768\n                ],\n                rotation: [\n                    0,\n                    Math.PI / 2,\n                    0\n                ],\n                scale: 0.04\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                castShadow: true,\n                receiveShadow: true,\n                geometry: nodes.Wizard_Staff3_Wizard_Staff3_0_3.geometry,\n                material: materials.Wizard_Staff3,\n                position: [\n                    -0.041,\n                    0.983,\n                    0.768\n                ],\n                rotation: [\n                    0,\n                    Math.PI / 2,\n                    0\n                ],\n                scale: 0.04\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                castShadow: true,\n                receiveShadow: true,\n                geometry: nodes.Wizard_Staff2_Wizard_Staff2_0.geometry,\n                material: materials.Wizard_Staff2,\n                position: [\n                    -0.041,\n                    0.983,\n                    0.768\n                ],\n                rotation: [\n                    0,\n                    Math.PI / 2,\n                    0\n                ],\n                scale: 0.04\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\models\\\\Staff.jsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Staff);\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload(\"/models/staff-transformed.glb\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/models/Staff.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/projects/ProjectLayout.jsx":
/*!***************************************************!*\
  !*** ./src/components/projects/ProjectLayout.jsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\nconst item = {\n    hidden: {\n        opacity: 0,\n        y: 100\n    },\n    show: {\n        opacity: 1,\n        y: 0\n    }\n};\nconst ProjectLink = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst ProjectLayout = ({ name, description, date, demoLink })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProjectLink, {\n        variants: item,\n        href: demoLink,\n        target: \"_blank\",\n        className: \" text-sm md:text-base flex  items-center justify-between w-full relative rounded-lg overflow-hidden p-4 md:p-6 custom-bg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-foreground\",\n                        children: name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted hidden sm:inline-block\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"self-end flex-1 mx-2 mb-1 bg-transparent border-b border-dashed border-muted\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-muted sm:text-foreground\",\n                children: new Date(date).toDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\ProjectLayout.jsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/projects/ProjectLayout.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/projects/index.jsx":
/*!*******************************************!*\
  !*** ./src/components/projects/index.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _ProjectLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProjectLayout */ \"(ssr)/./src/components/projects/ProjectLayout.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst container = {\n    hidden: {\n        opacity: 0\n    },\n    show: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.3,\n            delayChildren: 1.5\n        }\n    }\n};\nconst ProjectList = ({ projects })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        variants: container,\n        initial: \"hidden\",\n        animate: \"show\",\n        className: \"w-full max-w-auto  xl:max-w-4xl px-4 mx-auto lg:px-16 space-y-6 md:space-y-8 flex flex-col items-center\",\n        children: projects.map((project, index)=>{\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                ...project\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\index.jsx\",\n                lineNumber: 25,\n                columnNumber: 16\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\components\\\\projects\\\\index.jsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/projects/index.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"45cd407ded48\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLWNyZWF0aXZlLXBvcnRmb2xpby8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MGNkMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQ1Y2Q0MDdkZWQ0OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(sub pages)/layout.js":
/*!***************************************!*\
  !*** ./src/app/(sub pages)/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubPagesLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_HomeBtn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/HomeBtn */ \"(rsc)/./src/components/HomeBtn.jsx\");\n\n\nfunction SubPagesLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex min-h-screen flex-col items-center justify-center px-8 xs:px-16 lg:px-32 py-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeBtn__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\layout.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\layout.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhzdWIgcGFnZXMpL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEyQztBQUU1QixTQUFTQyxlQUFlLEVBQUVDLFFBQVEsRUFBRTtJQUNqRCxxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTs7MEJBQ2QsOERBQUNKLDJEQUFPQTs7Ozs7WUFDUEU7Ozs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1jcmVhdGl2ZS1wb3J0Zm9saW8vLi9zcmMvYXBwLyhzdWIgcGFnZXMpL2xheW91dC5qcz9kOWI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBIb21lQnRuIGZyb20gXCJAL2NvbXBvbmVudHMvSG9tZUJ0blwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTdWJQYWdlc0xheW91dCh7IGNoaWxkcmVuIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtOCB4czpweC0xNiBsZzpweC0zMiBweS0yMFwiPlxuICAgICAgPEhvbWVCdG4gLz5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSG9tZUJ0biIsIlN1YlBhZ2VzTGF5b3V0IiwiY2hpbGRyZW4iLCJtYWluIiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(sub pages)/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/(sub pages)/projects/page.js":
/*!**********************************************!*\
  !*** ./src/app/(sub pages)/projects/page.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _public_background_projects_background_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../public/background/projects-background.png */ \"(rsc)/./public/background/projects-background.png\");\n/* harmony import */ var _components_projects__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/projects */ \"(rsc)/./src/components/projects/index.jsx\");\n/* harmony import */ var _data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../data */ \"(rsc)/./src/app/data.js\");\n/* harmony import */ var _components_RenderModel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RenderModel */ \"(rsc)/./src/components/RenderModel.jsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dynamic */ \"(rsc)/./node_modules/next/dist/api/app-dynamic.js\");\n\n\n\n\n\n\n// import Staff from \"@/components/models/Staff\";\n\nconst Staff = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_models_Staff_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/models/Staff */ \"(rsc)/./src/components/models/Staff.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\(sub pages)\\\\projects\\\\page.js -> \" + \"@/components/models/Staff\"\n        ]\n    },\n    ssr: false\n});\nconst metadata = {\n    title: \"Projects\"\n};\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: _public_background_projects_background_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                alt: \"Next.js Portfolio website's about page background image\",\n                className: \"-z-50 fixed top-0 left-0 w-full h-full object-cover object-center opacity-50\",\n                priority: true,\n                sizes: \"100vw\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_projects__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                projects: _data__WEBPACK_IMPORTED_MODULE_4__.projectsData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center fixed  top-16  lg:top-20 -translate-x-1/2 lg:translate-x-0 -z-10 left-1/2 lg:-left-24 h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RenderModel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Staff, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\(sub pages)\\\\projects\\\\page.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(sub pages)/projects/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/data.js":
/*!*************************!*\
  !*** ./src/app/data.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BtnList: () => (/* binding */ BtnList),\n/* harmony export */   projectsData: () => (/* binding */ projectsData)\n/* harmony export */ });\n/*\nWebsites:\n\n- https://github.com/pmndrs/gltfjsx (GLTF JSX for 3D Models)\n- https://lucide.dev/icons/ (Lucide Icons)\n- https://github.com/anuraghazra/github-readme-stats (Github Readme Stats)\n- https://skillicons.dev (Skill Icons to show skills)\n- https://github-readme-streak-stats.herokuapp.com (Github Readme Streak Stats)\n\n:root {\n  --background: 27 27 27;\n  --foreground: 225 225 225;\n  --muted: 115 115 115;\n  --accent: 254 254 91; #FEFE5B\n}\n\n*/ const projectsData = [\n    {\n        id: 1,\n        name: \"EcoTracker\",\n        description: \"Track your carbon footprint\",\n        date: \"2022-08-15\",\n        demoLink: \"https://ecotracker.example.com\"\n    },\n    {\n        id: 2,\n        name: \"ArtGallery Online\",\n        description: \"Digital art showcase platform\",\n        date: \"2022-06-20\",\n        demoLink: \"https://artgalleryonline.example.com\"\n    },\n    {\n        id: 3,\n        name: \"BudgetPlanner\",\n        description: \"Plan and track expenses\",\n        date: \"2022-09-10\",\n        demoLink: \"https://budgetplanner.example.com\"\n    },\n    {\n        id: 4,\n        name: \"HealthBeat\",\n        description: \"Monitor heart rate zones\",\n        date: \"2022-05-30\",\n        demoLink: \"https://healthbeat.example.com\"\n    },\n    {\n        id: 5,\n        name: \"RecipeFinder\",\n        description: \"Discover new recipes\",\n        date: \"2022-07-12\",\n        demoLink: \"https://recipefinder.example.com\"\n    },\n    {\n        id: 6,\n        name: \"JourneyLogger\",\n        description: \"Log your travels\",\n        date: \"2022-10-01\",\n        demoLink: \"https://journeylogger.example.com\"\n    },\n    {\n        id: 7,\n        name: \"StudyBuddy\",\n        description: \"Collaborative learning platform\",\n        date: \"2022-04-18\",\n        demoLink: \"https://studybuddy.example.com\"\n    },\n    {\n        id: 8,\n        name: \"TechTalk\",\n        description: \"Tech news aggregator\",\n        date: \"2022-11-05\",\n        demoLink: \"https://techtalk.example.com\"\n    },\n    {\n        id: 9,\n        name: \"FitTrack\",\n        description: \"Fitness and workout tracker\",\n        date: \"2022-03-22\",\n        demoLink: \"https://fittrack.example.com\"\n    },\n    {\n        id: 10,\n        name: \"MindfulMoments\",\n        description: \"Meditation and mindfulness app\",\n        date: \"2022-02-14\",\n        demoLink: \"https://mindfulmoments.example.com\"\n    }\n];\nconst BtnList = [\n    {\n        label: \"Home\",\n        link: \"/\",\n        icon: \"home\",\n        newTab: false\n    },\n    {\n        label: \"About\",\n        link: \"/about\",\n        icon: \"about\",\n        newTab: false\n    },\n    {\n        label: \"Projects\",\n        link: \"/projects\",\n        icon: \"projects\",\n        newTab: false\n    },\n    {\n        label: \"Contact\",\n        link: \"/contact\",\n        icon: \"contact\",\n        newTab: false\n    },\n    {\n        label: \"Github\",\n        link: \"https://www.github.com/codebucks27\",\n        icon: \"github\",\n        newTab: true\n    },\n    {\n        label: \"LinkedIn\",\n        link: \"https://www.linkedin.com/in/codebucks\",\n        icon: \"linkedin\",\n        newTab: true\n    },\n    {\n        label: \"X\",\n        link: \"https://www.x.com/code_bucks\",\n        icon: \"twitter\",\n        newTab: true\n    },\n    {\n        label: \"Resume\",\n        link: \"/Kushal_Jetty.pdf\",\n        icon: \"resume\",\n        newTab: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/data.js\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _components_FireFliesBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FireFliesBackground */ \"(rsc)/./src/components/FireFliesBackground.jsx\");\n/* harmony import */ var _components_Sound__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sound */ \"(rsc)/./src/components/Sound.jsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        template: \"Next.js Portfolio Created with Three.js and Tailwind CSS | %s | CodeBucks\",\n        default: \"Next.js Portfolio Created with Three.js and Tailwind CSS by CodeBucks\"\n    },\n    description: \"A unique creative portfolio designed by CodeBucks with cutting-edge technologies like Next.js, Tailwind CSS, Three.js, and Framer Motion. Experience the art of modern web development firsthand. Checkout CodeBucks on youtube.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable), \"bg-background text-foreground font-inter\"),\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FireFliesBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sound__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"my-modal\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Classroom\\\\Projects\\\\Portfolio\\\\frontend\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/FireFliesBackground.jsx":
/*!************************************************!*\
  !*** ./src/components/FireFliesBackground.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Classroom\Projects\Portfolio\frontend\src\components\FireFliesBackground.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/HomeBtn.jsx":
/*!************************************!*\
  !*** ./src/components/HomeBtn.jsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Classroom\Projects\Portfolio\frontend\src\components\HomeBtn.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/RenderModel.jsx":
/*!****************************************!*\
  !*** ./src/components/RenderModel.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Classroom\Projects\Portfolio\frontend\src\components\RenderModel.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Sound.jsx":
/*!**********************************!*\
  !*** ./src/components/Sound.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Classroom\Projects\Portfolio\frontend\src\components\Sound.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/projects/index.jsx":
/*!*******************************************!*\
  !*** ./src/components/projects/index.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Classroom\Projects\Portfolio\frontend\src\components\projects\index.jsx#default`));


/***/ }),

/***/ "(rsc)/./public/background/projects-background.png":
/*!***************************************************!*\
  !*** ./public/background/projects-background.png ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/projects-background.fe38537d.png\",\"height\":576,\"width\":1024,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects-background.fe38537d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvYmFja2dyb3VuZC9wcm9qZWN0cy1iYWNrZ3JvdW5kLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywyTkFBMk4iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtY3JlYXRpdmUtcG9ydGZvbGlvLy4vcHVibGljL2JhY2tncm91bmQvcHJvamVjdHMtYmFja2dyb3VuZC5wbmc/ZDRjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvamVjdHMtYmFja2dyb3VuZC5mZTM4NTM3ZC5wbmdcIixcImhlaWdodFwiOjU3NixcIndpZHRoXCI6MTAyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZwcm9qZWN0cy1iYWNrZ3JvdW5kLmZlMzg1MzdkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./public/background/projects-background.png\n");

/***/ }),

/***/ "(ssr)/./public/background/projects-background.png":
/*!***************************************************!*\
  !*** ./public/background/projects-background.png ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/projects-background.fe38537d.png\",\"height\":576,\"width\":1024,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects-background.fe38537d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wdWJsaWMvYmFja2dyb3VuZC9wcm9qZWN0cy1iYWNrZ3JvdW5kLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywyTkFBMk4iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtY3JlYXRpdmUtcG9ydGZvbGlvLy4vcHVibGljL2JhY2tncm91bmQvcHJvamVjdHMtYmFja2dyb3VuZC5wbmc/Mzk1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvamVjdHMtYmFja2dyb3VuZC5mZTM4NTM3ZC5wbmdcIixcImhlaWdodFwiOjU3NixcIndpZHRoXCI6MTAyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZwcm9qZWN0cy1iYWNrZ3JvdW5kLmZlMzg1MzdkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./public/background/projects-background.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtY3JlYXRpdmUtcG9ydGZvbGlvLy4vc3JjL2FwcC9mYXZpY29uLmljbz9jZGJiIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/three","vendor-chunks/react-reconciler","vendor-chunks/framer-motion","vendor-chunks/three-stdlib","vendor-chunks/@react-three","vendor-chunks/fflate","vendor-chunks/scheduler","vendor-chunks/lucide-react","vendor-chunks/react-use-measure","vendor-chunks/its-fine","vendor-chunks/zustand","vendor-chunks/suspend-react","vendor-chunks/@swc","vendor-chunks/debounce","vendor-chunks/clsx","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(sub%20pages)%2Fprojects%2Fpage&page=%2F(sub%20pages)%2Fprojects%2Fpage&appPaths=%2F(sub%20pages)%2Fprojects%2Fpage&pagePath=private-next-app-dir%2F(sub%20pages)%2Fprojects%2Fpage.js&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CClassroom%5CProjects%5CPortfolio%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();