/*
Websites:

- https://github.com/pmndrs/gltfjsx (GLTF JSX for 3D Models)
- https://lucide.dev/icons/ (Lucide Icons)
- https://github.com/anuraghazra/github-readme-stats (Github Readme Stats)
- https://skillicons.dev (Skill Icons to show skills)
- https://github-readme-streak-stats.herokuapp.com (Github Readme Streak Stats)

:root {
  --background: 27 27 27;
  --foreground: 225 225 225;
  --muted: 115 115 115;
  --accent: 254 254 91; #FEFE5B
}

*/

export const projectsData = [
  {
    id: 1,
    name: "Disaster Recovery System",
    description: "AWS-based disaster recovery solution with automated failover and backup strategies",
    date: "2024-03-15",
    demoLink: "https://github.com/KushalJetty",
  },
  {
    id: 2,
    name: "Digital Signature System",
    description: "Secure digital signature implementation for HAL with cryptographic validation",
    date: "2024-02-20",
    demoLink: "https://github.com/KushalJetty",
  },
  {
    id: 3,
    name: "Get-Set-Pill",
    description: "IoT-based smart pill dispenser with automated scheduling and monitoring",
    date: "2024-01-10",
    demoLink: "https://github.com/KushalJetty",
  },
  {
    id: 4,
    name: "Car Rental System",
    description: "Salesforce CRM-based car rental management system with customer tracking",
    date: "2023-12-05",
    demoLink: "https://github.com/KushalJetty",
  },
  {
    id: 5,
    name: "Cruiser-Traveling",
    description: "Hotel booking platform with real-time availability and payment integration",
    date: "2023-11-12",
    demoLink: "https://github.com/KushalJetty",
  },
  {
    id: 6,
    name: "StreamzAI Testing Framework",
    description: "Automated testing framework with AI-powered test case generation and execution",
    date: "2023-10-01",
    demoLink: "https://github.com/KushalJetty",
  },
];

export const BtnList = [
  { label: "Home", link: "/", icon: "home", newTab: false },
  { label: "About", link: "/about", icon: "about", newTab: false },
  { label: "Projects", link: "/projects", icon: "projects", newTab: false },
  { label: "Contact", link: "/contact", icon: "contact", newTab: false },
  {
    label: "Github",
    link: "https://github.com/KushalJetty",
    icon: "github",
    newTab: true,
  },
  {
    label: "LinkedIn",
    link: "https://www.linkedin.com/in/kushaljetty/",
    icon: "linkedin",
    newTab: true,
  },
  {
    label: "GeeksforGeeks",
    link: "https://www.geeksforgeeks.org/user/kushaljetty24680/",
    icon: "twitter",
    newTab: true,
  },
  {
    label: "Resume",
    link: "/Kushal_Jetty.pdf",
    icon: "resume",
    newTab: true,
  },
];


