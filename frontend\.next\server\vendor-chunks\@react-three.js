"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/drei/core/Environment.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Environment.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: () => (/* binding */ Environment),\n/* harmony export */   EnvironmentCube: () => (/* binding */ EnvironmentCube),\n/* harmony export */   EnvironmentMap: () => (/* binding */ EnvironmentMap),\n/* harmony export */   EnvironmentPortal: () => (/* binding */ EnvironmentPortal)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/objects/GroundProjectedEnv.js\");\n/* harmony import */ var _useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useEnvironment.js */ \"(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js\");\n\n\n\n\n\n\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, blur = 0) {\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  // @ts-ignore\n  const oldBlur = target.backgroundBlurriness || 0;\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  // @ts-ignore\n  if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = blur;\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    // @ts-ignore\n    if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = oldBlur;\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  blur,\n  map\n}) {\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, blur);\n  }, [defaultScene, scene, map, background, blur]);\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  ...rest\n}) {\n  const texture = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(rest);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, blur);\n  }, [texture, background, scene, defaultScene, blur]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.gl);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  const camera = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const [virtualScene] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Scene());\n  const fbo = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const fbo = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = three__WEBPACK_IMPORTED_MODULE_4__.HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (frames === 1) camera.current.update(gl, virtualScene);\n    return setEnvProps(background, scene, defaultScene, fbo.texture, blur);\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.C)(() => {\n    if (frames === Infinity || count < frames) {\n      camera.current.update(gl, virtualScene);\n      count++;\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.g)( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(props);\n  const texture = props.map || textureDefault;\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.e)({\n    GroundProjectedEnvImpl: three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GroundProjectedEnv\n  }), []);\n  const args = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    map: texture\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentPortal, props) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, props);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Environment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js":
/*!***************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/useEnvironment.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnvironment: () => (/* binding */ useEnvironment)\n/* harmony export */ });\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/RGBELoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/EXRLoader.js\");\n/* harmony import */ var _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helpers/environment-assets.js */ \"(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js\");\n\n\n\n\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nfunction useEnvironment({\n  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  var _files$split$pop;\n  let loader = null;\n  let isCubeMap = false;\n  let extension;\n  if (preset) {\n    if (!(preset in _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_0__.presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(_helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_0__.presetsObj).join(', '));\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_0__.presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  isCubeMap = isArray(files);\n  extension = isArray(files) ? 'cube' : files.startsWith('data:application/exr') ? 'exr' : files.startsWith('data:application/hdr') ? 'hdr' : (_files$split$pop = files.split('.').pop()) == null || (_files$split$pop = _files$split$pop.split('?')) == null || (_files$split$pop = _files$split$pop.shift()) == null ? void 0 : _files$split$pop.toLowerCase();\n  loader = isCubeMap ? three__WEBPACK_IMPORTED_MODULE_1__.CubeTextureLoader : extension === 'hdr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_2__.RGBELoader : extension === 'exr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_3__.EXRLoader : null;\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const loaderResult = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F)(\n  // @ts-expect-error\n  loader, isCubeMap ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  const texture = isCubeMap ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  texture.mapping = isCubeMap ? three__WEBPACK_IMPORTED_MODULE_1__.CubeReflectionMapping : three__WEBPACK_IMPORTED_MODULE_1__.EquirectangularReflectionMapping;\n  const sRGBEncoding = 3001;\n  const LinearEncoding = 3000;\n  if ('colorSpace' in texture) texture.colorSpace = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? 'srgb' : 'srgb-linear';else texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;\n  return texture;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/useGLTF.js":
/*!********************************************************!*\
  !*** ./node_modules/@react-three/drei/core/useGLTF.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGLTF: () => (/* binding */ useGLTF)\n/* harmony export */ });\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/DRACOLoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/libs/MeshoptDecoder.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/GLTFLoader.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\");\n\n\n\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco, useMeshopt, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new three_stdlib__WEBPACK_IMPORTED_MODULE_0__.DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof three_stdlib__WEBPACK_IMPORTED_MODULE_1__.MeshoptDecoder === 'function' ? (0,three_stdlib__WEBPACK_IMPORTED_MODULE_1__.MeshoptDecoder)() : three_stdlib__WEBPACK_IMPORTED_MODULE_1__.MeshoptDecoder);\n    }\n  };\n}\nfunction useGLTF(path, useDraco = true, useMeshOpt = true, extendLoader) {\n  return (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(three_stdlib__WEBPACK_IMPORTED_MODULE_3__.GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\n}\nuseGLTF.preload = (path, useDraco = true, useMeshOpt = true, extendLoader) => _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F.preload(three_stdlib__WEBPACK_IMPORTED_MODULE_3__.GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\nuseGLTF.clear = input => _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F.clear(three_stdlib__WEBPACK_IMPORTED_MODULE_3__.GLTFLoader, input);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/useGLTF.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@react-three/drei/helpers/environment-assets.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   presetsObj: () => (/* binding */ presetsObj)\n/* harmony export */ });\nconst presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2RyZWkvaGVscGVycy9lbnZpcm9ubWVudC1hc3NldHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtY3JlYXRpdmUtcG9ydGZvbGlvLy4vbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL2hlbHBlcnMvZW52aXJvbm1lbnQtYXNzZXRzLmpzPzI0MzAiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcHJlc2V0c09iaiA9IHtcbiAgYXBhcnRtZW50OiAnbGVib21ib18xay5oZHInLFxuICBjaXR5OiAncG90c2RhbWVyX3BsYXR6XzFrLmhkcicsXG4gIGRhd246ICdraWFyYV8xX2Rhd25fMWsuaGRyJyxcbiAgZm9yZXN0OiAnZm9yZXN0X3Nsb3BlXzFrLmhkcicsXG4gIGxvYmJ5OiAnc3RfZmFnYW5zX2ludGVyaW9yXzFrLmhkcicsXG4gIG5pZ2h0OiAnZGlraG9sb2xvX25pZ2h0XzFrLmhkcicsXG4gIHBhcms6ICdyb29pdG91X3BhcmtfMWsuaGRyJyxcbiAgc3R1ZGlvOiAnc3R1ZGlvX3NtYWxsXzAzXzFrLmhkcicsXG4gIHN1bnNldDogJ3ZlbmljZV9zdW5zZXRfMWsuaGRyJyxcbiAgd2FyZWhvdXNlOiAnZW1wdHlfd2FyZWhvdXNlXzAxXzFrLmhkcidcbn07XG5cbmV4cG9ydCB7IHByZXNldHNPYmogfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/index-8afac004.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useThree),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useFrame),\n/* harmony export */   D: () => (/* binding */ useGraph),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useLoader),\n/* harmony export */   a: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   b: () => (/* binding */ createRoot),\n/* harmony export */   c: () => (/* binding */ createEvents),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ context),\n/* harmony export */   g: () => (/* binding */ createPortal),\n/* harmony export */   h: () => (/* binding */ reconciler),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ applyProps),\n/* harmony export */   k: () => (/* binding */ dispose),\n/* harmony export */   l: () => (/* binding */ invalidate),\n/* harmony export */   m: () => (/* binding */ advance),\n/* harmony export */   n: () => (/* binding */ addEffect),\n/* harmony export */   o: () => (/* binding */ addAfterEffect),\n/* harmony export */   p: () => (/* binding */ addTail),\n/* harmony export */   q: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   r: () => (/* binding */ render),\n/* harmony export */   s: () => (/* binding */ getRootState),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useMutableCallback),\n/* harmony export */   v: () => (/* binding */ act),\n/* harmony export */   w: () => (/* binding */ buildGraph),\n/* harmony export */   x: () => (/* binding */ roots),\n/* harmony export */   y: () => (/* binding */ useInstanceHandle),\n/* harmony export */   z: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance instanceof three__WEBPACK_IMPORTED_MODULE_4__.BufferGeometry) instance.__r3f.attach = 'geometry';else if (instance instanceof three__WEBPACK_IMPORTED_MODULE_4__.Material) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-ignore\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, warn on undefined behavior\n  const handleTextInstance = () => console.warn('Text is not allowed in the R3F tree! This could be stray whitespace or characters.');\n  const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-ignore\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  var _instance$__r3f;\n  const localState = (_instance$__r3f = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f : {};\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f2;\n    // Bail out on primitive object\n    if ((_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState.memoizedProps && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState.memoizedProps && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && \"development\" !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f3, _root$getState, _instance$__r3f4;\n  // Filter equals, events and reserved props\n  const localState = (_instance$__r3f3 = instance.__r3f) != null ? _instance$__r3f3 : {};\n  const root = localState.root;\n  const rootState = (_root$getState = root == null ? void 0 : root.getState == null ? void 0 : root.getState()) != null ? _root$getState : {};\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-ignore\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        const isColor = targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Color;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if (currentInstance[key] instanceof three__WEBPACK_IMPORTED_MODULE_4__.Texture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === three__WEBPACK_IMPORTED_MODULE_4__.RGBAFormat && currentInstance[key].type === three__WEBPACK_IMPORTED_MODULE_4__.UnsignedByteType) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f4 = instance.__r3f) != null && _instance$__r3f4.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f5, _instance$__r3f5$root;\n  const state = (_instance$__r3f5 = instance.__r3f) == null ? void 0 : (_instance$__r3f5$root = _instance$__r3f5.root) == null ? void 0 : _instance$__r3f5$root.getState == null ? void 0 : _instance$__r3f5$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n    default:\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = (0,zustand__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((set, get) => {\n    const position = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n    const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n    const tempTarget = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target instanceof three__WEBPACK_IMPORTED_MODULE_4__.Vector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new three__WEBPACK_IMPORTED_MODULE_4__.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new three__WEBPACK_IMPORTED_MODULE_4__.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState()), frames);\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    /**\r\n     * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n     * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n     */\n    invalidate,\n    /**\r\n     * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n     * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n     */\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`)))))).finally(() => loader.dispose == null ? void 0 : loader.dispose());\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.suspend)(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.preload)(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.clear)([Proto, ...keys]);\n};\n\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_4__.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof three__WEBPACK_IMPORTED_MODULE_4__.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_4__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_4__.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) applyProps(camera, cameraOptions);\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions instanceof three__WEBPACK_IMPORTED_MODULE_4__.Scene) {\n          scene = sceneOptions;\n        } else {\n          scene = new three__WEBPACK_IMPORTED_MODULE_4__.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: three__WEBPACK_IMPORTED_MODULE_4__.BasicShadowMap,\n            percentage: three__WEBPACK_IMPORTED_MODULE_4__.PCFShadowMap,\n            soft: three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap,\n            variance: three__WEBPACK_IMPORTED_MODULE_4__.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n\n      // Set color space and tonemapping preferences\n      const LinearEncoding = 3000;\n      const sRGBEncoding = 3001;\n      applyProps(gl, {\n        outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n        toneMapping: flat ? three__WEBPACK_IMPORTED_MODULE_4__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_4__.ACESFilmicToneMapping\n      });\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(context.Provider, {\n    value: store\n  }, children);\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Portal, {\n    key: container.uuid,\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Raycaster());\n  const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector2());\n  const inject = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_4__.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = (0,zustand__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reconciler.createPortal( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(context.Provider, {\n    value: usePortalStore\n  }, children), usePortalStore, null));\n}\nreconciler.injectIntoDevTools({\n  bundleType:  false ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: react__WEBPACK_IMPORTED_MODULE_0__.version\n});\nconst act = react__WEBPACK_IMPORTED_MODULE_0__.unstable_act;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   act: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   addEffect: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   addTail: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   advance: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   applyProps: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   context: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   createEvents: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createPortal: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createRoot: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   dispose: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   events: () => (/* binding */ createPointerEvents),\n/* harmony export */   extend: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getRootState: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   invalidate: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   reconciler: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   render: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useGraph: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   useLoader: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useStore: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useThree: () => (/* reexport safe */ _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)\n/* harmony export */ });\n/* harmony import */ var _index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-8afac004.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-8afac004.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/web.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\nconst CanvasImpl = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  react__WEBPACK_IMPORTED_MODULE_2__.useMemo(() => (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n  const Bridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_7__.useContextBridge)();\n  const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  const divRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(onPointerMissed);\n  const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_2__.useState(false);\n  const [error, setError] = react__WEBPACK_IMPORTED_MODULE_2__.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Bridge, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n        set: setError\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n          set: setBlock\n        })\n      }, children))));\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => (0,_index_8afac004_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    }\n  }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", {\n    ref: containerRef,\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"canvas\", {\n    ref: canvasRef,\n    style: {\n      display: 'block'\n    }\n  }, fallback)));\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(its_fine__WEBPACK_IMPORTED_MODULE_7__.FiberProvider, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CanvasImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, props, {\n    ref: ref\n  })));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ })

};
;