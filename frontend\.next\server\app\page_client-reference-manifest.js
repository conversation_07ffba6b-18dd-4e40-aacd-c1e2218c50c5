globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./public/background/home-background.png":{"*":{"id":"(ssr)/./public/background/home-background.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/models/Wizard.jsx":{"*":{"id":"(ssr)/./src/components/models/Wizard.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/navigation/index.jsx":{"*":{"id":"(ssr)/./src/components/navigation/index.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/RenderModel.jsx":{"*":{"id":"(ssr)/./src/components/RenderModel.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FireFliesBackground.jsx":{"*":{"id":"(ssr)/./src/components/FireFliesBackground.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Sound.jsx":{"*":{"id":"(ssr)/./src/components/Sound.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./public/background/about-background.png":{"*":{"id":"(ssr)/./public/background/about-background.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/about/ItemLayout.jsx":{"*":{"id":"(ssr)/./src/components/about/ItemLayout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/models/HatModel.jsx":{"*":{"id":"(ssr)/./src/components/models/HatModel.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HomeBtn.jsx":{"*":{"id":"(ssr)/./src/components/HomeBtn.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./public/background/projects-background.png":{"*":{"id":"(ssr)/./public/background/projects-background.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/models/Staff.jsx":{"*":{"id":"(ssr)/./src/components/models/Staff.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/projects/index.jsx":{"*":{"id":"(ssr)/./src/components/projects/index.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\public\\background\\home-background.png":{"id":"(app-pages-browser)/./public/background/home-background.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\models\\Wizard.jsx":{"id":"(app-pages-browser)/./src/components/models/Wizard.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\navigation\\index.jsx":{"id":"(app-pages-browser)/./src/components/navigation/index.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\RenderModel.jsx":{"id":"(app-pages-browser)/./src/components/RenderModel.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\FireFliesBackground.jsx":{"id":"(app-pages-browser)/./src/components/FireFliesBackground.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\Sound.jsx":{"id":"(app-pages-browser)/./src/components/Sound.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\public\\background\\about-background.png":{"id":"(app-pages-browser)/./public/background/about-background.png","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\about\\ItemLayout.jsx":{"id":"(app-pages-browser)/./src/components/about/ItemLayout.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\models\\HatModel.jsx":{"id":"(app-pages-browser)/./src/components/models/HatModel.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\HomeBtn.jsx":{"id":"(app-pages-browser)/./src/components/HomeBtn.jsx","name":"*","chunks":["app/(sub pages)/layout","static/chunks/app/(sub%20pages)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\public\\background\\projects-background.png":{"id":"(app-pages-browser)/./public/background/projects-background.png","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\models\\Staff.jsx":{"id":"(app-pages-browser)/./src/components/models/Staff.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\projects\\index.jsx":{"id":"(app-pages-browser)/./src/components/projects/index.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\app\\(sub pages)\\layout":[]}}