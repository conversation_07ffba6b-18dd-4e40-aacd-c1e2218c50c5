"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use-measure";
exports.ids = ["vendor-chunks/react-use-measure"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-use-measure/dist/web.js":
/*!****************************************************!*\
  !*** ./node_modules/react-use-measure/dist/web.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMeasure)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! debounce */ \"(ssr)/./node_modules/debounce/index.js\");\n/* harmony import */ var debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(debounce__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction useMeasure(_temp) {\n  let {\n    debounce,\n    scroll,\n    polyfill,\n    offsetSize\n  } = _temp === void 0 ? {\n    debounce: 0,\n    scroll: false,\n    offsetSize: false\n  } : _temp;\n  const ResizeObserver = polyfill || (typeof window === 'undefined' ? class ResizeObserver {} : window.ResizeObserver);\n\n  if (!ResizeObserver) {\n    throw new Error('This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills');\n  }\n\n  const [bounds, set] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    left: 0,\n    top: 0,\n    width: 0,\n    height: 0,\n    bottom: 0,\n    right: 0,\n    x: 0,\n    y: 0\n  }); // keep all state in a ref\n\n  const state = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    element: null,\n    scrollContainers: null,\n    resizeObserver: null,\n    lastBounds: bounds\n  }); // set actual debounce values early, so effects know if they should react accordingly\n\n  const scrollDebounce = debounce ? typeof debounce === 'number' ? debounce : debounce.scroll : null;\n  const resizeDebounce = debounce ? typeof debounce === 'number' ? debounce : debounce.resize : null; // make sure to update state only as long as the component is truly mounted\n\n  const mounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    mounted.current = true;\n    return () => void (mounted.current = false);\n  }); // memoize handlers, so event-listeners know when they should update\n\n  const [forceRefresh, resizeChange, scrollChange] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const callback = () => {\n      if (!state.current.element) return;\n      const {\n        left,\n        top,\n        width,\n        height,\n        bottom,\n        right,\n        x,\n        y\n      } = state.current.element.getBoundingClientRect();\n      const size = {\n        left,\n        top,\n        width,\n        height,\n        bottom,\n        right,\n        x,\n        y\n      };\n\n      if (state.current.element instanceof HTMLElement && offsetSize) {\n        size.height = state.current.element.offsetHeight;\n        size.width = state.current.element.offsetWidth;\n      }\n\n      Object.freeze(size);\n      if (mounted.current && !areBoundsEqual(state.current.lastBounds, size)) set(state.current.lastBounds = size);\n    };\n\n    return [callback, resizeDebounce ? debounce__WEBPACK_IMPORTED_MODULE_1___default()(callback, resizeDebounce) : callback, scrollDebounce ? debounce__WEBPACK_IMPORTED_MODULE_1___default()(callback, scrollDebounce) : callback];\n  }, [set, offsetSize, scrollDebounce, resizeDebounce]); // cleanup current scroll-listeners / observers\n\n  function removeListeners() {\n    if (state.current.scrollContainers) {\n      state.current.scrollContainers.forEach(element => element.removeEventListener('scroll', scrollChange, true));\n      state.current.scrollContainers = null;\n    }\n\n    if (state.current.resizeObserver) {\n      state.current.resizeObserver.disconnect();\n      state.current.resizeObserver = null;\n    }\n  } // add scroll-listeners / observers\n\n\n  function addListeners() {\n    if (!state.current.element) return;\n    state.current.resizeObserver = new ResizeObserver(scrollChange);\n    state.current.resizeObserver.observe(state.current.element);\n\n    if (scroll && state.current.scrollContainers) {\n      state.current.scrollContainers.forEach(scrollContainer => scrollContainer.addEventListener('scroll', scrollChange, {\n        capture: true,\n        passive: true\n      }));\n    }\n  } // the ref we expose to the user\n\n\n  const ref = node => {\n    if (!node || node === state.current.element) return;\n    removeListeners();\n    state.current.element = node;\n    state.current.scrollContainers = findScrollContainers(node);\n    addListeners();\n  }; // add general event listeners\n\n\n  useOnWindowScroll(scrollChange, Boolean(scroll));\n  useOnWindowResize(resizeChange); // respond to changes that are relevant for the listeners\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    removeListeners();\n    addListeners();\n  }, [scroll, scrollChange, resizeChange]); // remove all listeners when the components unmounts\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => removeListeners, []);\n  return [ref, bounds, forceRefresh];\n} // Adds native resize listener to window\n\n\nfunction useOnWindowResize(onWindowResize) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const cb = onWindowResize;\n    window.addEventListener('resize', cb);\n    return () => void window.removeEventListener('resize', cb);\n  }, [onWindowResize]);\n}\n\nfunction useOnWindowScroll(onScroll, enabled) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (enabled) {\n      const cb = onScroll;\n      window.addEventListener('scroll', cb, {\n        capture: true,\n        passive: true\n      });\n      return () => void window.removeEventListener('scroll', cb, true);\n    }\n  }, [onScroll, enabled]);\n} // Returns a list of scroll offsets\n\n\nfunction findScrollContainers(element) {\n  const result = [];\n  if (!element || element === document.body) return result;\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = window.getComputedStyle(element);\n  if ([overflow, overflowX, overflowY].some(prop => prop === 'auto' || prop === 'scroll')) result.push(element);\n  return [...result, ...findScrollContainers(element.parentElement)];\n} // Checks if element boundaries are equal\n\n\nconst keys = ['x', 'y', 'top', 'bottom', 'left', 'right', 'width', 'height'];\n\nconst areBoundsEqual = (a, b) => keys.every(key => a[key] === b[key]);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-use-measure/dist/web.js\n");

/***/ })

};
;