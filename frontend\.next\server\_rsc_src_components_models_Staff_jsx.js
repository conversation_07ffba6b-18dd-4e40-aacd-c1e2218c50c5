"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_components_models_Staff_jsx";
exports.ids = ["_rsc_src_components_models_Staff_jsx"];
exports.modules = {

/***/ "(rsc)/./src/components/models/Staff.jsx":
/*!*****************************************!*\
  !*** ./src/components/models/Staff.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Desktop\\Classroom\\Projects\\Portfolio\\frontend\\src\\components\\models\\Staff.jsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/models/Staff.jsx\n");

/***/ })

};
;