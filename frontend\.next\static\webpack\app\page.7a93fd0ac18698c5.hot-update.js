"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/data.js":
/*!*************************!*\
  !*** ./src/app/data.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BtnList: function() { return /* binding */ BtnList; },\n/* harmony export */   projectsData: function() { return /* binding */ projectsData; }\n/* harmony export */ });\n/*\nWebsites:\n\n- https://github.com/pmndrs/gltfjsx (GLTF JSX for 3D Models)\n- https://lucide.dev/icons/ (Lucide Icons)\n- https://github.com/anuraghazra/github-readme-stats (Github Readme Stats)\n- https://skillicons.dev (Skill Icons to show skills)\n- https://github-readme-streak-stats.herokuapp.com (Github Readme Streak Stats)\n\n:root {\n  --background: 27 27 27;\n  --foreground: 225 225 225;\n  --muted: 115 115 115;\n  --accent: 254 254 91; #FEFE5B\n}\n\n*/ const projectsData = [\n    {\n        id: 1,\n        name: \"EcoTracker\",\n        description: \"Track your carbon footprint\",\n        date: \"2022-08-15\",\n        demoLink: \"https://ecotracker.example.com\"\n    },\n    {\n        id: 2,\n        name: \"ArtGallery Online\",\n        description: \"Digital art showcase platform\",\n        date: \"2022-06-20\",\n        demoLink: \"https://artgalleryonline.example.com\"\n    },\n    {\n        id: 3,\n        name: \"BudgetPlanner\",\n        description: \"Plan and track expenses\",\n        date: \"2022-09-10\",\n        demoLink: \"https://budgetplanner.example.com\"\n    },\n    {\n        id: 4,\n        name: \"HealthBeat\",\n        description: \"Monitor heart rate zones\",\n        date: \"2022-05-30\",\n        demoLink: \"https://healthbeat.example.com\"\n    },\n    {\n        id: 5,\n        name: \"RecipeFinder\",\n        description: \"Discover new recipes\",\n        date: \"2022-07-12\",\n        demoLink: \"https://recipefinder.example.com\"\n    },\n    {\n        id: 6,\n        name: \"JourneyLogger\",\n        description: \"Log your travels\",\n        date: \"2022-10-01\",\n        demoLink: \"https://journeylogger.example.com\"\n    },\n    {\n        id: 7,\n        name: \"StudyBuddy\",\n        description: \"Collaborative learning platform\",\n        date: \"2022-04-18\",\n        demoLink: \"https://studybuddy.example.com\"\n    },\n    {\n        id: 8,\n        name: \"TechTalk\",\n        description: \"Tech news aggregator\",\n        date: \"2022-11-05\",\n        demoLink: \"https://techtalk.example.com\"\n    },\n    {\n        id: 9,\n        name: \"FitTrack\",\n        description: \"Fitness and workout tracker\",\n        date: \"2022-03-22\",\n        demoLink: \"https://fittrack.example.com\"\n    },\n    {\n        id: 10,\n        name: \"MindfulMoments\",\n        description: \"Meditation and mindfulness app\",\n        date: \"2022-02-14\",\n        demoLink: \"https://mindfulmoments.example.com\"\n    }\n];\nconst BtnList = [\n    {\n        label: \"Home\",\n        link: \"/\",\n        icon: \"home\",\n        newTab: false\n    },\n    {\n        label: \"About\",\n        link: \"/about\",\n        icon: \"about\",\n        newTab: false\n    },\n    {\n        label: \"Projects\",\n        link: \"/projects\",\n        icon: \"projects\",\n        newTab: false\n    },\n    {\n        label: \"Contact\",\n        link: \"/contact\",\n        icon: \"contact\",\n        newTab: false\n    },\n    {\n        label: \"Github\",\n        link: \"https://www.github.com/codebucks27\",\n        icon: \"github\",\n        newTab: true\n    },\n    {\n        label: \"LinkedIn\",\n        link: \"https://www.linkedin.com/in/codebucks\",\n        icon: \"linkedin\",\n        newTab: true\n    },\n    {\n        label: \"X\",\n        link: \"https://www.x.com/code_bucks\",\n        icon: \"twitter\",\n        newTab: true\n    },\n    {\n        label: \"Resume\",\n        link: \"/Kushal_Jetty.pdf\",\n        icon: \"resume\",\n        newTab: true\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/data.js\n"));

/***/ })

});